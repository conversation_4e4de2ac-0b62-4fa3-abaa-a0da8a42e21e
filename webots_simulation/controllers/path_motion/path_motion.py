from controller import Supervisor
import math
import argparse
import sys
from circle_model import CircleModel
from polygon_model import PolygonModel
from line_model import LineModel


class CircularMotionRobot:
    """控制机器人进行圆周运动的类"""

    def __init__(self):
        """初始化机器人控制器"""
        self.TIME_STEP = 32
        self.supervisor = Supervisor()
        self.args = self._parse_arguments()

        # 获取机器人节点
        self.robot_node = self._init_robot()
        self.trans_field = self.robot_node.getField("translation")
        self.init_position = self._parse_init_position()
        self.simulation_time = self.supervisor.getTime()

        # 碰撞检测相关
        self.is_stopped = False  # 停止状态标志阈值(米)
        self.webots_vehicle_node = None  # WEBOTS_VEHICLE0节点引用
        self.pause_start_time = 0.0  # 开始暂停的仿真时间
        self.distance_sensor = self.supervisor.getDevice("UltrasonicSensor")
        if self.distance_sensor is not None:
            self.distance_sensor.enable(self.TIME_STEP)

        # 根据类型创建相应的运动模型
        if self.args.type == "circle":
            self.motion_model = CircleModel(
                radius=self.args.radius,
                velocity=self.args.velocity,
                init_pos=self.init_position,
            )
        elif self.args.type == "polygon":
            if not self.args.points:
                print("未指定多边形顶点，使用默认的三角形")
                points = [(1.0, 0.0), (-0.5, 0.866), (-0.5, -0.866)]
            else:
                points = PolygonModel.parse_points(self.args.points)
            self.motion_model = PolygonModel(
                points=points,
                velocity=self.args.velocity,
            )
        elif self.args.type == "line":
            if not self.args.points:
                print("未指定路径点，使用默认的直线")
                points = [(-1.0, 0.0), (1.0, 0.0)]
            else:
                points = LineModel.parse_points(self.args.points)
            self.motion_model = LineModel(
                points=points,
                velocity=self.args.velocity,
            )
        else:
            print(f"暂不支持 {self.args.type} 类型的运动，使用默认的圆形运动")
            self.motion_model = CircleModel(
                radius=self.args.radius, velocity=self.args.velocity
            )

    def _parse_arguments(self):
        """解析命令行参数"""
        parser = argparse.ArgumentParser(description="机器人运动控制器")

        parser.add_argument(
            "--type",
            type=str,
            default="circle",
            choices=["circle", "polygon", "line"],
            help="运动类型，可选：circle/polygon/line，默认circle",
        )
        parser.add_argument(
            "--radius", type=float, default=1.0, help="圆形运动半径(米)，默认1.0"
        )
        parser.add_argument(
            "--velocity", type=float, default=1.0, help="运动速度(米/秒)，默认1.0"
        )
        parser.add_argument(
            "--points", type=str, help="多边形顶点坐标，格式：x1,y1;x2,y2;x3,y3..."
        )
        parser.add_argument("--init_pos", type=str, help="xyz坐标，格式：x,y,z")

        if "--help" in sys.argv or "-h" in sys.argv:
            parser.print_help()
            sys.exit(0)

        args_str = " ".join(sys.argv[1:])
        args_list = args_str.split()

        try:
            return parser.parse_args(args_list)
        except SystemExit:
            print("参数解析错误，使用默认值")
            return parser.parse_args([])

    def _parse_init_position(self):
        """解析初始位置"""
        if self.args.init_pos:
            try:
                # 解析命令行参数中的初始位置
                x, y, z = map(float, self.args.init_pos.split(","))
                print(f"str {self.args.init_pos} init_pos: {x}, {y}, {z}")
                return [x, y, z]
            except (ValueError, AttributeError):
                return [0.0, 0.0, 0.0]

        # 如果没有指定初始位置或解析失败，使用当前位置
        return self.trans_field.getSFVec3f()

    def _init_robot(self):
        """初始化机器人节点"""
        robot_node = self.supervisor.getSelf()
        if robot_node is None:
            sys.stderr.write("No DEF MY_ROBOT node found in the current world file\n")
            sys.exit(1)
        return robot_node

    def _get_current_position(self):
        """获取当前机器人位置"""
        return self.trans_field.getSFVec3f()

    def update_position(self, elapsed_time):
        """更新机器人位置"""
        position = self.motion_model.calculate_position(elapsed_time)
        # print(position)
        self.trans_field.setSFVec3f(position)
        # 更新机器人角度
        rotation_field = self.robot_node.getField("rotation")
        new_angle = self.motion_model.calculate_angle(elapsed_time)
        rotation_field.setSFRotation([0, 0, 1, new_angle])

        velocity = self.motion_model.calculate_velocity(elapsed_time)
        self.robot_node.setVelocity(velocity)

    def get_device_distance(self):
        if self.distance_sensor is not None:
            value = self.distance_sensor.getValue()
        else:
            value = math.inf
        return value

    def check_collision(self):
        """检查碰撞"""
        device_distance = self.get_device_distance()

        if (
            device_distance is not math.inf
            and device_distance < 5.0
            and not self.is_stopped
        ):
            # 记录停止时的时间状态
            current_time = self.supervisor.getTime()
            self.pause_start_time = current_time
            self.is_stopped = True
            self.robot_node.setVelocity([0, 0, 0, 0, 0, 0])
            print(
                self.supervisor.getName(),
                "detect collision, pause_start_time:",
                self.pause_start_time,
            )
        elif (
            device_distance is not math.inf
            and device_distance > 5.0
            and self.is_stopped
        ):
            current_time = self.supervisor.getTime()
            paused_duration = current_time - self.pause_start_time
            self.simulation_time = self.simulation_time + paused_duration
            self.is_stopped = False
            print(self.supervisor.getName(), "resume , current_time:", current_time)

    def run(self):
        """运行机器人控制循环"""
        while self.supervisor.step(self.TIME_STEP) != -1:
            current_time = self.supervisor.getTime()

            # 检查碰撞
            self.check_collision()

            # 如果没有停止，继续更新位置
            if not self.is_stopped:
                elapsed = current_time - self.simulation_time
                self.update_position(elapsed)


def main():
    """主函数"""
    robot = CircularMotionRobot()
    robot.run()


if __name__ == "__main__":
    main()
