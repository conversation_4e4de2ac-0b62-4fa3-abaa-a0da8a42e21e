#VRML_SIM R2023b utf8
# DBox Proto - A configurable robot with box geometry and ultrasonic sensor
# Used for path motion simulation with customizable parameters

IMPORTABLE EXTERNPROTO "UltrasonicSensor.proto"

PROTO DBox [
  field SFVec3f translation 0 0 0
  field SFRotation rotation 0 0 1 0
  field SFString name "DBox"
  field SFString controller "path_motion"
  field MFString controllerArgs [
    "--type=circle"
    "--radius=10.000000"
    "--velocity=5.000000"
    "--init_pos=0,0,0"
  ]
  field SFBool supervisor TRUE
  field SFVec3f boxSize 3 3 3
  field SFColor boxColor 0 0 1
  field SFVec3f ultrasonicSensorTranslation 1.5 0 0.5
  field SFVec3f linearVelocity 0 0 0
  field SFVec3f angularVelocity 0 0 0
]
{
  Robot {
    translation IS translation
    rotation IS rotation
    name IS name
    controller IS controller
    controllerArgs IS controllerArgs
    supervisor IS supervisor
    linearVelocity IS linearVelocity
    angularVelocity IS angularVelocity
    children [
      UltrasonicSensor {
        translation IS ultrasonicSensorTranslation
      }
      Shape {
        appearance Appearance {
          material Material {
            diffuseColor IS boxColor
          }
        }
        geometry Box {
          size IS boxSize
        }
      }
    ]
    boundingObject Box {
      size IS boxSize
    }
  }
}
