#VRML_SIM R2023b utf8
# DBox Proto - A configurable robot with box geometry and ultrasonic sensor
# Used for path motion simulation with customizable parameters

IMPORTABLE EXTERNPROTO "UltrasonicSensor.proto"

PROTO DBox [
  field SFVec3f translation 0 0 0
  field SFString name "<PERSON><PERSON>"
  field SFString controller "path_motion"
  field SFVec3f boxSize 3 3 3
]
{
  Robot {
    translation IS translation
    rotation 0 0 1 0
    name IS name
    controller IS controller
    controllerArgs [
      "--type=circle"
      "--radius=10.000000"
      "--velocity=5.000000"
      "--init_pos=0,0,0"
    ]
    supervisor TRUE
    children [
      UltrasonicSensor {
        translation %<= boxSize.x / 2 >% 0 0.5
      }
      Shape {
        appearance Appearance {
          material Material {
            diffuseColor 0 0 1
          }
        }
        geometry Box {
          size IS boxSize
        }
      }
    ]
    boundingObject Box {
      size IS boxSize
    }
  }
}
